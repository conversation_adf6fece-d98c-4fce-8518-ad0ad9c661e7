// import 'package:flutter/material.dart';
// import 'package:fruitly/styled_text.dart';

// // class GradientContainer extends StatelessWidget {
// //   const GradientContainer({super.key});

// //   @override
// //   Widget build(BuildContext context) {
// //     return Container(
// //       decoration: const BoxDecoration(
// //         gradient: LinearGradient(colors: [
// //           Colors.blue, Colors.red
// //         ],
// //         begin: Alignment.topLeft,
// //         end: Alignment.bottomRight,
// //         ),
// //       ),
// //       child: Center(
// //         child: Image.asset('assets/images/sign_in_bg.jpg'),
// //         // child: Image.asset('assets/images/apple.jpg'),
// //         // child: Image.asset('assets/images/meta.jpg'),
// //         // child: Image.asset('assets/images/google.jpg'),
// //         // child: StyledText(),
// //       ),
// //     );
// //   }
// // }

// class GradientContainer extends StatelessWidget {
//   const GradientContainer({super.key});

//   @override
//   Widget build(BuildContext context) {
//     // return Container(
//     //   width: double.infinity,  // 전체 너비 채우기
//     //   height: double.infinity, // 전체 높이 채우기
//     //   decoration: const BoxDecoration(
//     //     image: DecorationImage(
//     //       image: AssetImage('assets/images/sign_in_bg.jpg'),
//     //       fit: BoxFit.cover, // 화면 전체를 덮도록 설정
//     //     ),
//     //   ),
//     //   child: Center(
//     //     // 여기에 로그인 폼이나 다른 위젯들을 추가
//     //     child: Text('Welcome', style: TextStyle(color: Colors.white)),
//     //     // child: StyledText(), // 필요시 사용
//     //   ),
//     // );

//     return Stack(
//       children: [
//         Container(
//           width: double.infinity,
//           height: double.infinity,
//           decoration: const BoxDecoration(
//             image: DecorationImage(
//               image: AssetImage('assets/images/sign_in_bg.jpg'),
//               fit: BoxFit.cover,
//             ),
//           ),
//         ),
//         Container(
//           width: double.infinity,
//           height: double.infinity,
//           decoration: BoxDecoration(
//             gradient: LinearGradient(
//               colors: [
//                 Colors.transparent,
//                 Colors.black.withOpacity(0.4),
//               ],
//               begin: Alignment.topCenter,
//               end: Alignment.bottomCenter,
//             ),
//           ),
//         ),
//         Center(
//           child: Text('Welcome', style: TextStyle(color: Colors.white)),
//         ),
//       ],
//     );
//   }
// }
