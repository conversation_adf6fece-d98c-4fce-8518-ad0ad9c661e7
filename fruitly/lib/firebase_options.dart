// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCYU1YghehHDSRoRcb8wW82ibf2b1AWpDo',
    appId: '1:397601082043:web:be5f00e5b5d43001dd779e',
    messagingSenderId: '397601082043',
    projectId: 'frultly',
    authDomain: 'frultly.firebaseapp.com',
    storageBucket: 'frultly.firebasestorage.app',
    measurementId: 'G-5JT21SJ7MY',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBzd-XE6NGiUXJMpf1U3994LRLrpUyHs_A',
    appId: '1:397601082043:android:1cc8c655e31136a9dd779e',
    messagingSenderId: '397601082043',
    projectId: 'frultly',
    storageBucket: 'frultly.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyA82eqW-Bv4H-8Cqpd9xhwnABoQ8jBZTxI',
    appId: '1:397601082043:ios:84aa2412b29d77cedd779e',
    messagingSenderId: '397601082043',
    projectId: 'frultly',
    storageBucket: 'frultly.firebasestorage.app',
    iosBundleId: 'com.example.fruitly',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyA82eqW-Bv4H-8Cqpd9xhwnABoQ8jBZTxI',
    appId: '1:397601082043:ios:84aa2412b29d77cedd779e',
    messagingSenderId: '397601082043',
    projectId: 'frultly',
    storageBucket: 'frultly.firebasestorage.app',
    iosBundleId: 'com.example.fruitly',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCYU1YghehHDSRoRcb8wW82ibf2b1AWpDo',
    appId: '1:397601082043:web:ac77461c9615ad66dd779e',
    messagingSenderId: '397601082043',
    projectId: 'frultly',
    authDomain: 'frultly.firebaseapp.com',
    storageBucket: 'frultly.firebasestorage.app',
    measurementId: 'G-VR4LH9KT2B',
  );
}
