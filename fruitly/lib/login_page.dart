// // lib/login_page.dart
// import 'package:flutter/material.dart';
// import 'dart:ui';  // added for using ImageFilter

// class LoginPage extends StatelessWidget {
//   const LoginPage({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Container(
//         width: double.infinity,
//         height: double.infinity,
//         decoration: BoxDecoration(
//           image: DecorationImage(
//             image: AssetImage('assets/images/sign_in_bg.png'),
//             fit: BoxFit.cover,
//           ),
//         ),
//         child: Center(
//           child: SizedBox(
//             width: 350,
//             height: 600,
//             child: ClipRRect(
//               borderRadius: BorderRadius.circular(20),
//               child: BackdropFilter(
//                 filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
//                 child: Container(
//                   decoration: BoxDecoration(
//                     color: Colors.white.withOpacity(0.05),
//                     borderRadius: BorderRadius.circular(50),
//                     border: Border.all(
//                       color: Colors.white.withOpacity(0.35),
//                     ),
//                   ),
//                   child: Padding(
//                     padding: EdgeInsets.all(32),
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         // Welcome text
//                         Text(
//                           'Welcome,',
//                           style: TextStyle(
//                             color: Colors.white,
//                             fontSize: 48,
//                             fontWeight: FontWeight.bold,
//                           ),
//                         ),
//                         SizedBox(height: 25),

//                         // Username input field
//                         SizedBox(
//                           width: 300, // 고정 너비로 정렬 맞춤
//                           child: TextField(
//                             style: TextStyle(color: Colors.white),
//                             decoration: InputDecoration(
//                               hintText: 'username',
//                               hintStyle: TextStyle(
//                                 color: Colors.white.withOpacity(0.7),
//                               ),
//                               filled: true,
//                               fillColor: Colors.black.withOpacity(0.3),
//                               border: OutlineInputBorder(
//                                 borderRadius: BorderRadius.circular(15),
//                                 borderSide: BorderSide.none,
//                               ),
//                               contentPadding: EdgeInsets.symmetric(
//                                 horizontal: 20,
//                                 vertical: 13
//                               ),
//                             ),
//                           ),
//                         ),
//                         SizedBox(height: 25),

//                         // Password input field
//                         SizedBox(
//                           width: 300, // 고정 너비로 정렬 맞춤
//                           child: TextField(
//                             obscureText: true,
//                             style: TextStyle(color: Colors.white),
//                             decoration: InputDecoration(
//                               hintText: 'password',
//                               hintStyle: TextStyle(
//                                 color: Colors.white.withOpacity(0.7),
//                               ),
//                               filled: true,
//                               fillColor: Colors.black.withOpacity(0.3),
//                               border: OutlineInputBorder(
//                                 borderRadius: BorderRadius.circular(15),
//                                 borderSide: BorderSide.none,
//                               ),
//                               contentPadding: EdgeInsets.symmetric(
//                                 horizontal: 20,
//                                 vertical: 13
//                               ),
//                             ),
//                           ),
//                         ),
//                         SizedBox(height: 25),

//                         // Social login text
//                         Text(
//                           'sign in using other accounts',
//                           style: TextStyle(
//                             color: Colors.white.withOpacity(0.8),
//                             fontSize: 14,
//                           ),
//                         ),
//                         SizedBox(height: 20),

//                         // Social login buttons
//                         SizedBox(
//                           width: 300, // 입력 필드와 같은 너비
//                           child: Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: [
//                               // Meta login - GestureDetector 추가
//                               GestureDetector(
//                                 onTap: () {
//                                   print('Meta login button pressed!');
//                                   // 여기에 실제 Meta 로그인 로직 추가
//                                   ScaffoldMessenger.of(context).showSnackBar(
//                                     SnackBar(content: Text('Meta login pressed!')),
//                                   );
//                                 },
//                                 child: _buildSocialButton('assets/images/meta.png'),
//                               ),
//                               // Google login - GestureDetector 추가
//                               GestureDetector(
//                                 onTap: () {
//                                   print('Google login button pressed!');
//                                   // 여기에 실제 Google 로그인 로직 추가
//                                   ScaffoldMessenger.of(context).showSnackBar(
//                                     SnackBar(content: Text('Google login pressed!')),
//                                   );
//                                 },
//                                 child: _buildSocialButton('assets/images/google.png'),
//                               ),
//                               // Apple login - GestureDetector 추가
//                               GestureDetector(
//                                 onTap: () {
//                                   print('Apple login button pressed!');
//                                   // 여기에 실제 Apple 로그인 로직 추가
//                                   ScaffoldMessenger.of(context).showSnackBar(
//                                     SnackBar(content: Text('Apple login pressed!')),
//                                   );
//                                 },
//                                 child: _buildSocialButton('assets/images/apple.png'),
//                               ),
//                             ],
//                           ),
//                         ),
//                         SizedBox(height: 30),

//                         // Sign in 버튼 - 다른 요소들과 같은 너비로 정렬
//                         SizedBox(
//                           width: 300, // 입력 필드와 같은 너비
//                           height: 50,
//                           child: ElevatedButton(
//                             onPressed: () {
//                               print('Sign in button pressed');
//                               // 실제 로그인 로직 추가
//                             },
//                             style: ElevatedButton.styleFrom(
//                               backgroundColor: Colors.white,
//                               foregroundColor: Colors.black,
//                               shape: RoundedRectangleBorder(
//                                 borderRadius: BorderRadius.circular(15),
//                               ),
//                             ),
//                             child: Text(
//                               'Sign in',
//                               style: TextStyle(
//                                 fontSize: 15,
//                                 fontWeight: FontWeight.w600,
//                               ),
//                             ),
//                           ),
//                         ),
//                         SizedBox(height: 30),

//                         // 회원가입 링크
//                         GestureDetector(
//                           onTap: () {
//                             print('Sign up link pressed');
//                             // 회원가입 페이지로 이동 로직 추가
//                           },
//                           child: Text(
//                             'don\'t have an account? Click here to sign up',
//                             style: TextStyle(
//                               color: Colors.white.withOpacity(0.8),
//                               fontSize: 12,
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   // 소셜 버튼을 만드는 헬퍼 메소드 (GestureDetector 제거)
//   Widget _buildSocialButton(String imagePath) {
//     return Container(
//       width: 70,
//       height: 70,
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(15),
//       ),
//       child: Center(
//         child: Image.asset(
//           imagePath,
//           width: 30,
//           height: 30,
//         ),
//       ),
//     );
//   }
// }



// lib/login_page.dart
import 'package:flutter/material.dart';
import 'dart:ui';  // added for using ImageFilter
import 'package:firebase_auth/firebase_auth.dart';
import 'services/auth_service.dart';
import 'home_page.dart'; // 다음에 만들 예정

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final AuthService _authService = AuthService();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _isLoading = false;

  // 로그인 처리 함수
  Future<void> _signIn() async {
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Type in email and password')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      await _authService.signInWithEmail(
        _emailController.text.trim(),
        _passwordController.text.trim(),
      );

      // 로그인 성공 시 홈 페이지로 이동
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => HomePage()),
        );
      }
    } on FirebaseAuthException catch (e) {
      String message = 'Login failed';
      if (e.code == 'user-not-found') {
        message = 'user not found';
      } else if (e.code == 'wrong-password') {
        message = 'wrong password';
      } else if (e.code == 'invalid-email') {
        message = 'invalid email';
      } else if (e.code == 'invalid-credential') {
        message = 'invalid credential';
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('로그인 중 오류가 발생했습니다'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/sign_in_bg.png'),
            fit: BoxFit.cover,
          ),
        ),
        child: Center(
          child: SizedBox(
            width: 350,
            height: 600,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(50),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.35),
                    ),
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(32),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Welcome text
                        Text(
                          'Welcome,',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 48,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 25),

                        // Email input field (username에서 email로 변경)
                        SizedBox(
                          width: 300, // 고정 너비로 정렬 맞춤
                          child: TextField(
                            controller: _emailController,
                            keyboardType: TextInputType.emailAddress,
                            style: TextStyle(color: Colors.white),
                            decoration: InputDecoration(
                              hintText: 'email',
                              hintStyle: TextStyle(
                                color: Colors.white.withOpacity(0.7),
                              ),
                              filled: true,
                              fillColor: Colors.black.withOpacity(0.3),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(15),
                                borderSide: BorderSide.none,
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 13
                              ),
                              prefixIcon: Icon(
                                Icons.email_outlined,
                                color: Colors.white.withOpacity(0.7),
                                size: 20,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 25),

                        // Password input field (컨트롤러 추가)
                        SizedBox(
                          width: 300, // 고정 너비로 정렬 맞춤
                          child: TextField(
                            controller: _passwordController,
                            obscureText: true,
                            style: TextStyle(color: Colors.white),
                            decoration: InputDecoration(
                              hintText: 'password',
                              hintStyle: TextStyle(
                                color: Colors.white.withOpacity(0.7),
                              ),
                              filled: true,
                              fillColor: Colors.black.withOpacity(0.3),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(15),
                                borderSide: BorderSide.none,
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 13
                              ),
                              prefixIcon: Icon(
                                Icons.lock_outline,
                                color: Colors.white.withOpacity(0.7),
                                size: 20,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 25),

                        // Social login text
                        Text(
                          'sign in using other accounts',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.8),
                            fontSize: 14,
                          ),
                        ),
                        SizedBox(height: 20),

                        // Social login buttons (기존과 동일)
                        SizedBox(
                          width: 300, // 입력 필드와 같은 너비
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Meta login - GestureDetector 추가
                              GestureDetector(
                                onTap: () {
                                  print('Meta login button pressed!');
                                  // 여기에 실제 Meta 로그인 로직 추가
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(content: Text('Meta login pressed!')),
                                  );
                                },
                                child: _buildSocialButton('assets/images/meta.png'),
                              ),
                              // Google login - GestureDetector 추가
                              GestureDetector(
                                onTap: () {
                                  print('Google login button pressed!');
                                  // 여기에 실제 Google 로그인 로직 추가
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(content: Text('Google login pressed!')),
                                  );
                                },
                                child: _buildSocialButton('assets/images/google.png'),
                              ),
                              // Apple login - GestureDetector 추가
                              GestureDetector(
                                onTap: () {
                                  print('Apple login button pressed!');
                                  // 여기에 실제 Apple 로그인 로직 추가
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(content: Text('Apple login pressed!')),
                                  );
                                },
                                child: _buildSocialButton('assets/images/apple.png'),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 30),

                        // Sign in 버튼 (Firebase Auth 연동)
                        SizedBox(
                          width: 300, // 입력 필드와 같은 너비
                          height: 50,
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _signIn,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: Colors.black,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(15),
                              ),
                            ),
                            child: _isLoading
                                ? SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      color: Colors.black,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : Text(
                                    'Sign in',
                                    style: TextStyle(
                                      fontSize: 15,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                          ),
                        ),
                        SizedBox(height: 30),

                        // 회원가입 링크 (향후 회원가입 페이지 연결 예정)
                        GestureDetector(
                          onTap: () {
                            print('Sign up link pressed');
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('회원가입 기능 준비 중...')),
                            );
                          },
                          child: Text(
                            'don\'t have an account? Click here to sign up',
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.8),
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 소셜 버튼을 만드는 헬퍼 메소드 (기존과 동일)
  Widget _buildSocialButton(String imagePath) {
    return Container(
      width: 70,
      height: 70,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Center(
        child: Image.asset(
          imagePath,
          width: 30,
          height: 30,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
