// lib/home_page.dart
import 'package:flutter/material.dart';
import 'services/auth_service.dart';
import 'login_page.dart';

class HomePage extends StatelessWidget {
  HomePage({super.key});
  final AuthService _authService = AuthService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Home Page - TEMP'),
        actions: [
          IconButton(
            icon: Icon(Icons.logout),
            onPressed: () async {
              await _authService.signOut();
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => LoginPage()),
              );
            },
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Login successful! 🎉',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Sized<PERSON><PERSON>(height: 20),
            Text(
              'User: ${_authService.currentUser?.email ?? "Unknown"}',
              style: TextStyle(fontSize: 16),
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 40),
            ElevatedButton(
              onPressed: () {
                // 여기에 카메라 페이지로 이동하는 코드 추가 예정
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('카메라 기능 준비 중...')),
                );
              },
              child: Text('카메라로 이동'),
            ),
          ],
        ),
      ),
    );
  }
}
