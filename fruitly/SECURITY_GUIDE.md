# 🔐 보안 가이드 - GitHub에 올리면 안 되는 파일들

## ❌ 절대 GitHub에 올리면 안 되는 파일들

### Firebase 관련 파일들
```
firebase.json
lib/firebase_options.dart
android/app/google-services.json
ios/Runner/GoogleService-Info.plist
```

### 소셜 로그인 설정 파일들
```
android/app/src/main/res/values/strings.xml
ios/Runner/Info.plist (Facebook/Apple ID가 포함된 경우)
```

### 환경 변수 파일들
```
.env
.env.local
.env.production
.env.staging
```

## ✅ GitHub에 올려도 되는 파일들

### 코드 파일들
```
lib/main.dart
lib/login_page.dart
lib/home_page.dart
lib/services/auth_service.dart
pubspec.yaml
```

### 설정 파일들 (민감한 정보 제거 후)
```
android/app/build.gradle.kts
android/app/src/main/AndroidManifest.xml (Facebook ID 제거 후)
ios/Podfile
```

### 문서 파일들
```
README.md
SOCIAL_LOGIN_SETUP.md
SECURITY_GUIDE.md
```

## 🛠️ 현재 해야 할 작업

1. **민감한 파일들을 git에서 제거**:
   ```bash
   git rm --cached lib/firebase_options.dart
   git rm --cached firebase.json
   git rm --cached android/app/google-services.json
   git rm --cached ios/Runner/GoogleService-Info.plist
   ```

2. **안전한 파일들만 커밋**:
   ```bash
   git add lib/main.dart
   git add lib/login_page.dart
   git add lib/home_page.dart
   git add lib/services/auth_service.dart
   git add pubspec.yaml
   git add .gitignore
   git add README.md
   git add SOCIAL_LOGIN_SETUP.md
   git add SECURITY_GUIDE.md
   ```

3. **커밋 및 푸시**:
   ```bash
   git commit -m "Add social login implementation (Google, Facebook, Apple)"
   git push origin dev/youngjae_hur/login_feature_dev
   ```

## 🔄 팀원들과 공유하는 방법

1. **템플릿 파일 생성**: 민감한 정보를 제거한 템플릿 파일들을 만들어 공유
2. **문서화**: SOCIAL_LOGIN_SETUP.md에 설정 방법 상세히 기록
3. **환경 변수 사용**: 가능하면 환경 변수로 민감한 정보 관리

## ⚠️ 이미 올린 경우 대처 방법

만약 실수로 민감한 파일을 이미 GitHub에 올렸다면:

1. **즉시 Firebase 프로젝트의 API 키 재생성**
2. **Facebook/Apple 앱 설정에서 키 재생성**
3. **Git 히스토리에서 완전 제거**:
   ```bash
   git filter-branch --force --index-filter \
   'git rm --cached --ignore-unmatch lib/firebase_options.dart' \
   --prune-empty --tag-name-filter cat -- --all
   ```

## 📝 권장사항

- 개발 초기부터 .gitignore 설정을 철저히 하기
- 민감한 정보는 환경 변수나 별도 설정 파일로 관리
- 정기적으로 보안 검토 실시
- 팀원들에게 보안 가이드 공유
