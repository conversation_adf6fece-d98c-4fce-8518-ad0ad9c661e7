{"flutter": {"platforms": {"android": {"default": {"projectId": "frultly", "appId": "1:397601082043:android:1cc8c655e31136a9dd779e", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "frultly", "appId": "1:397601082043:ios:84aa2412b29d77cedd779e", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "frultly", "appId": "1:397601082043:ios:84aa2412b29d77cedd779e", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "frultly", "configurations": {"android": "1:397601082043:android:1cc8c655e31136a9dd779e", "ios": "1:397601082043:ios:84aa2412b29d77cedd779e", "macos": "1:397601082043:ios:84aa2412b29d77cedd779e", "web": "1:397601082043:web:be5f00e5b5d43001dd779e", "windows": "1:397601082043:web:ac77461c9615ad66dd779e"}}}}}}